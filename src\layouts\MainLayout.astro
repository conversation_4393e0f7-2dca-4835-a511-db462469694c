---
interface Props {
  title?: string;
  description?: string;
  showHeader?: boolean;
  showBackButton?: boolean;
  headerTitle?: string;
  showFooter?: boolean;
  image?: string;
  canonicalURL?: string;
  type?: 'website' | 'article' | 'product';
  keywords?: string;
  author?: string;
  publishDate?: Date;
  modifiedDate?: Date;
  schema?: any;
}

const {
  title = "Sreekar Publishers - Educational Materials",
  description = "Quality educational materials for students from 6th to 10th grade. Telugu and English Medium study resources, textbooks, and exam preparation materials.",
  showHeader = true,
  showBackButton = false,
  headerTitle = "", // Default is empty
  showFooter = true,
  image = "/images/highq-foods-social-image.jpg", // Default social image
  canonicalURL,
  type = "website",
  keywords = "educational materials, study resources, Telugu medium, English medium, 6th to 10th grade, textbooks, exam preparation, Sreekar Publishers, Eluru",
  author = "Sreekar Publishers Team",
  publishDate,
  modifiedDate,
  schema,
} = Astro.props;

// Import global styles
import "../styles/global.css";

// Safely generate URLs to avoid Invalid URL errors
const siteUrl = Astro.site ? Astro.site.toString() : 'https://sreekarpublishers.com/';
const baseUrl = siteUrl.endsWith('/') ? siteUrl : `${siteUrl}/`;
const currentPath = Astro.url.pathname;
const currentUrl = new URL(currentPath.startsWith('/') ? currentPath.slice(1) : currentPath, baseUrl);
const finalCanonicalURL = canonicalURL || currentUrl.href;

// Generate the absolute URL for the social image
let socialImageURL;
try {
  // Handle both absolute and relative image paths
  socialImageURL = image.startsWith('http') ? image : new URL(image.startsWith('/') ? image.slice(1) : image, baseUrl).href;
} catch (e) {
  // Fallback if URL creation fails
  socialImageURL = `${baseUrl}images/sreekar-publishers-social-image.png`;
}

// Schema.org JSON-LD
const defaultSchema = {
  "@context": "https://schema.org",
  "@type": "BookStore",
  "name": "Sreekar Publishers",
  "url": baseUrl,
  "logo": `${baseUrl}images/icons/icon-512x512.png`,
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "Door No: 2-56, CHANIKYAPURI COLONY 2ND LINE, 34 MC DIVISION, NEAR D MART",
    "addressLocality": "ELURU URBAN",
    "addressRegion": "ELURU",
    "postalCode": "534002",
    "addressCountry": "IN"
  },
  "geo": {
    "@type": "GeoCoordinates",
    "latitude": "16.7107",
    "longitude": "81.1031"
  },
  "telephone": "+91-9876543210",
  "educationalUse": ["Study Materials", "Textbooks", "Reference Books", "Exam Preparation"],
  "priceRange": "₹₹",
  "openingHours": "Mo-Su 08:00-22:00",
  "sameAs": [
    "https://facebook.com/sreekarpublishers",
    "https://instagram.com/sreekarpublishers"
  ]
};

const jsonLD = schema || defaultSchema;
---

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" type="image/png" href="/images/icons/icon-192x192.png" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, viewport-fit=cover, maximum-scale=1.0, user-scalable=no"
    />

    <!-- Primary Meta Tags -->
    <title>{title}</title>
    <meta name="title" content={title} />
    <meta name="description" content={description} />
    <meta name="keywords" content={keywords} />
    <meta name="author" content={author} />
    <link rel="canonical" href={finalCanonicalURL} />

    {publishDate && <meta name="date" content={publishDate.toISOString()} />}
    {modifiedDate && <meta name="revised" content={modifiedDate.toISOString()} />}

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content={type} />
    <meta property="og:url" content={finalCanonicalURL} />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={socialImageURL} />
    <meta property="og:site_name" content="Sreekar Publishers" />
    <meta property="og:locale" content="en_IN" />
    <link rel="sitemap" href="/sitemap-index.xml" />
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:url" content={finalCanonicalURL} />
    <meta name="twitter:title" content={title} />
    <meta name="twitter:description" content={description} />
    <meta name="twitter:image" content={socialImageURL} />
    <meta name="twitter:creator" content="@sreekarpublishers" />

    <!-- iOS PWA Meta Tags -->
    <meta name="theme-color" content="#FF6B35" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Sreekar Publishers" />

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/images/icons/icon-192x192.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/images/icons/icon-192x192.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/images/icons/icon-192x192.png" />
    <link rel="apple-touch-icon" sizes="167x167" href="/images/icons/icon-192x192.png" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Immediate Service Worker Registration -->
    <script is:inline>
      // Register Service Worker as early as possible
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/sw.js')
          .then(registration => {
            console.log('Service Worker registered with scope:', registration.scope);
          })
          .catch(error => {
            console.error('Service Worker registration failed:', error);
          });
      }
    </script>

    <!-- Structured data -->
    <script type="application/ld+json" set:html={JSON.stringify(jsonLD)} />

    <!-- Preload Critical Resources -->
    <link rel="preload" as="font" href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" crossorigin />
    <link rel="preload" as="font" href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" crossorigin />

    <!-- Google Fonts for better typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <!-- Material Icons for navigation -->
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons+Round"
      rel="stylesheet"
    />
  </head>
  <body class="flex flex-col bg-gray-50">
    <!-- Status Bar Spacer for iOS devices -->
    <div id="status-bar-spacer" class="w-full bg-white z-50"></div>

    <!-- Redesigned Professional Native-Style Header -->
    {
      showHeader && (
        <header
          id="app-header"
          class="sticky top-0 z-30 bg-white flex items-center justify-between border-b border-gray-100"
        >
          <div class="w-full max-w-md mx-auto flex items-center justify-between px-4 h-14">
            <div class="flex items-center flex-1">
              {showBackButton ? (
                <button
                  onclick="history.back()"
                  class="flex items-center justify-center -ml-2 p-2.5 rounded-full hover:bg-gray-50 active:bg-gray-100 transition-all"
                  aria-label="Go back"
                >
                  <span class="material-icons-round text-gray-800">
                    arrow_back
                  </span>
                </button>
              ) : (
                <div class="app-logo flex items-center">
                  <div class="w-10 h-10 rounded-xl flex items-center justify-center shadow-sm overflow-hidden">
                    <img
                      src="/images/icons/icon-192x192.png"
                      alt="Sreekar Publishers Logo"
                      width="40"
                      height="40"
                      class="w-full h-full object-cover"
                    />
                  </div>
                  {headerTitle ? (
                    <h1 class="text-lg font-semibold text-gray-800 ml-3">
                      {headerTitle}
                    </h1>
                  ) : (
                    <div class="ml-3">
                      <h1 class="text-lg font-bold text-gray-800 leading-tight">
                        Sreekar Publishers
                      </h1>
                      <p class="text-xs text-gray-500 -mt-1">
                        Premium Food Delivery
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>

            <div class="flex items-center gap-2">
              <a
                href="/cart"
                class="p-2.5 rounded-full hover:bg-gray-50 active:bg-gray-100 transition-colors inline-flex items-center justify-center relative"
                aria-label="Cart"
              >
                <span class="material-icons-round text-gray-700">
                  shopping_bag
                </span>
                <span
                  id="cart-badge"
                  class="absolute -top-0.5 -right-0.5 bg-[#5466F7] text-white rounded-full w-5 h-5 text-[10px] font-semibold flex items-center justify-center border-2 border-white shadow-sm"
                  aria-hidden="true"
                >
                  0
                </span>
              </a>
            </div>
          </div>
        </header>
      )
    }

    <main class="flex-1 pb-20 pt-0">
      <slot />
    </main>

    <!-- Redesigned Native-like Bottom Navigation to match the image -->
    {
      showFooter && (
        <nav
          class="fixed bottom-0 left-0 right-0 bg-white flex justify-around items-center shadow-lg z-40 border-t border-gray-100"
          style="height: calc(60px + var(--safe-area-inset-bottom)); padding-bottom: var(--safe-area-inset-bottom);"
          aria-label="Main navigation"
        >
          <a
            href="/"
            class="nav-tab flex flex-col items-center justify-center h-full flex-1 relative pt-1.5 pb-1"
            data-target="home"
            aria-label="Home"
          >
            <div class="nav-icon-container rounded-full w-12 h-12 flex items-center justify-center mb-0.5">
              <span class="material-icons-round text-[20px]">home</span>
            </div>
            <span class="text-[10px] font-medium">Home</span>
          </a>
          <a
            href="/favorites"
            class="nav-tab flex flex-col items-center justify-center h-full flex-1 relative pt-1.5 pb-1"
            data-target="favorites"
            aria-label="Favorites"
          >
            <div class="nav-icon-container rounded-full w-12 h-12 flex items-center justify-center mb-0.5">
              <span class="material-icons-round text-[20px]">favorite</span>
            </div>
            <span class="text-[10px] font-medium">Favorites</span>
          </a>

          <a
            href="/orders"
            class="nav-tab flex flex-col items-center justify-center h-full flex-1 relative pt-1.5 pb-1"
            data-target="projects"
            aria-label="Orders"
          >
            <div class="nav-icon-container rounded-full w-12 h-12 flex items-center justify-center mb-0.5">
              <span class="material-icons-round text-[20px]">category</span>
            </div>
            <span class="text-[10px] font-medium">Orders</span>
          </a>
          <a
            href="/cart"
            class="nav-tab flex flex-col items-center justify-center h-full flex-1 relative pt-1.5 pb-1"
            data-target="cart"
            aria-label="Cart"
          >
            <div class="nav-icon-container rounded-full w-12 h-12 flex items-center justify-center mb-0.5 relative">
              <span class="material-icons-round text-[20px]">
                shopping_cart
              </span>
              <span class="absolute -top-0.5 -right-0.5 bg-[#FF6B35] text-white rounded-full w-4 h-4 text-[10px] font-semibold flex items-center justify-center border border-white">
                3
              </span>
            </div>
            <span class="text-[10px] font-medium">Cart</span>
          </a>
          <a
            href="/profile"
            class="nav-tab flex flex-col items-center justify-center h-full flex-1 relative pt-1.5 pb-1"
            data-target="profile"
            aria-label="Profile"
          >
            <div class="nav-icon-container rounded-full w-12 h-12 flex items-center justify-center mb-0.5">
              <span class="material-icons-round text-[20px]">person</span>
            </div>
            <span class="text-[10px] font-medium">Profile</span>
          </a>
        </nav>
      )
    }

    <!-- App Scripts -->
    <script is:inline src="/scripts/app.js"></script>
    <script is:inline src="/scripts/swipe.js"></script>
    <script is:inline src="/scripts/utils-initializer.js"></script>

    <!-- Initial Setup Script -->
    <script is:inline>
      // Initial setup on first page load
      document.addEventListener("DOMContentLoaded", () => {
        // Initialize status bar, nav tabs, and touch feedback
        // These will be reinitialized on page transitions by transition-handler.js
        if (typeof initStatusBar === 'function') initStatusBar();
        if (typeof initNavTabs === 'function') initNavTabs();
        if (typeof initTouchFeedback === 'function') initTouchFeedback();
      });
    </script>

    <!-- Add utility scripts -->
    <script src="/scripts/cart-utils.js" is:inline></script>
    <script src="/scripts/favorites-utils.js" is:inline></script>
    <script src="/scripts/api-client.js" is:inline></script>
    <script src="/scripts/auth-utils.js" is:inline></script>

    <!-- Add transition handler scripts -->
    <script src="/scripts/transition-handler.js" is:inline></script>
    <script src="/scripts/react-transition-handler.js" is:inline></script>

    <!-- Add progress bar for page transitions -->
    <script src="/scripts/progress-bar.js" is:inline></script>

    <script is:inline>
      // Initialize on first page load
      document.addEventListener("DOMContentLoaded", () => {
        // Initialize utilities
        if (window.UtilsInitializer) {
          window.UtilsInitializer.initAllUtils().then(({ cartUtils }) => {
            if (cartUtils) {
              cartUtils.updateCartBadge();
            }
          });
        } else if (window.CartUtils) {
          window.CartUtils.updateCartBadge();
        }

        // Add scroll behavior for header
        const header = document.getElementById("app-header");
        if (header) {
          let lastScrollTop = 0;

          window.addEventListener("scroll", () => {
            const scrollTop =
              window.pageYOffset || document.documentElement.scrollTop;

            // Add shadow when scrolling down
            if (scrollTop > 10) {
              header.classList.add("shadow-sm");
            } else {
              header.classList.remove("shadow-sm");
            }

            lastScrollTop = scrollTop;
          });
        }
      });

      // Also initialize on Astro page transitions
      document.addEventListener("astro:page-load", () => {
        if (window.UtilsInitializer) {
          window.UtilsInitializer.initAllUtils().then(({ cartUtils }) => {
            if (cartUtils) {
              cartUtils.updateCartBadge();
            }
          });
        } else if (window.CartUtils) {
          window.CartUtils.updateCartBadge();
        }
      });
    </script>
  </body>
</html>
