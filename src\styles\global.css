@import "tailwindcss";

/* Global Styles */
/* @tailwind base;
@tailwind components;
@tailwind utilities; */

/* Custom properties - these will supplement Tailwind */
:root {
  --primary: #FF6B35;
  --primary-dark: #E85D29;
  --secondary: #2EC4B6;
  --accent: #FFBC42;
  --safe-area-inset-bottom: env(safe-area-inset-bottom, 0);
  --safe-area-inset-top: env(safe-area-inset-top, 0);
  --safe-area-inset-left: env(safe-area-inset-left, 0px);
  --safe-area-inset-right: env(safe-area-inset-right, 0px);
}

/* Base styles that extend Tailwind */
html, body {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  overscroll-behavior: none;
  /* Prevent text highlighting when tapping on elements */
  -webkit-user-select: none;
  user-select: none;
  /* Disable zoom on double tap */
  touch-action: manipulation;
  /* Prevent rubber-band scrolling on iOS */
  position: fixed;
  overflow: hidden;
  width: 100%;
  height: 100%;
  /* System font optimization */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

html {
  font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  @apply bg-gray-50;
  -webkit-tap-highlight-color: transparent;
  overscroll-behavior-y: none;
  height: calc(100% + var(--safe-area-inset-bottom));
  scroll-behavior: smooth;
}

body {
  /* Allow scrolling within the body */
  overflow-y: auto;
  /* Smooth scrolling for content */
  scroll-behavior: smooth;
  /* Prevent overflow on x axis */
  overflow-x: hidden;
  /* Add iOS-like momentum scrolling */
  -webkit-overflow-scrolling: touch;
  /* Base background color */
  background-color: #f9f9f9;
  /* Updated for new nav height */
  padding-bottom: calc(60px + var(--safe-area-inset-bottom));
  /* Improved visual consistency */
  color: #334155;
  line-height: 1.5;
  min-height: 100vh;
  min-height: -webkit-fill-available;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior-y: none;
  -webkit-font-smoothing: antialiased;
  @apply bg-gray-50;
}

/* Prevent blue highlight on tap */
* {
  -webkit-tap-highlight-color: transparent;
}

main {
  /* Ensure main content fills available space */
  min-height: calc(100vh - 56px - var(--safe-area-inset-bottom) - var(--safe-area-inset-top));
}

/* Utility classes beyond what Tailwind provides */
.no-tap-highlight {
  -webkit-tap-highlight-color: transparent;
}

/* Native-like button feedback */
.nav-tab, a, button {
  -webkit-tap-highlight-color: transparent;
  transition: transform 0.15s ease, opacity 0.15s ease;
}

/* Touch feedback similar to native apps */
.touch-active {
  opacity: 0.8;
  transition: opacity 0.15s;
}

/* Material-like ripple effect */
@keyframes ripple {
  from { opacity: 1; transform: scale(0); }
  to { opacity: 0; transform: scale(2.5); }
}

/* Navbar tab styles */
.nav-tab.active .nav-icon {
  transform: translateY(-3px);
  transition: transform 0.3s ease;
}

.nav-tab .nav-indicator {
  transition: opacity 0.3s ease;
}

.nav-tab {
  transition: all 0.3s ease;
}

.nav-tab .nav-icon-container {
  transition: background-color 0.25s ease, transform 0.2s ease;
  transform: scale(1);
}

.nav-tab:active .nav-icon-container {
  transform: scale(0.92);
}

.nav-tab.active .nav-icon-container {
  box-shadow: 0 2px 8px rgba(84, 102, 247, 0.25);
}

.nav-tab .material-icons-round {
  transition: color 0.25s ease;
}

.nav-tab span:not(.material-icons-round) {
  transition: color 0.25s ease, font-weight 0.25s ease;
}

.nav-tab.active .nav-icon-container {
  animation: pop 0.4s ease;
}

@keyframes pop {
  0% { transform: scale(0.9); }
  40% { transform: scale(1.1); }
  80% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

/* Native-like card styles */
.card {
  border-radius: 12px;
  background-color: white;
  overflow: hidden;
  will-change: transform;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.card:active {
  transform: scale(0.98);
}

/* Enhanced shadow for a more modern look */
.shadow-up {
  box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.05);
}

/* iOS-like back gesture area */
.back-gesture-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 100%;
  z-index: 30;
}

/* Animation keyframes */
@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Native app-like transition animations */
@keyframes slide-up {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-slide-up {
  animation: slide-up 0.3s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
}

.animate-fade-in {
  animation: fade-in 0.3s ease forwards;
}

.page-transition {
  animation: pageSlide 0.3s ease forwards;
}

@keyframes pageSlide {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Hide scrollbars but keep functionality */
.scrollbar-hide {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* Progress bar styles */
.highq-progress-bar {
  position: fixed;
  top: 0;
  left: 0;
  height: 3px;
  background-color: #FF6B35;
  background-image: linear-gradient(
    to right,
    rgba(255, 107, 53, 0.8),
    rgba(255, 107, 53, 1),
    rgba(255, 107, 53, 0.8)
  );
  z-index: 9999;
  width: 0%;
  transition: width 0.3s cubic-bezier(0.65, 0, 0.35, 1), opacity 0.3s ease;
  pointer-events: none;
  box-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
  animation: progress-pulse 1.5s ease-in-out infinite;
}

@keyframes progress-pulse {
  0% {
    box-shadow: 0 0 5px rgba(255, 107, 53, 0.5);
  }
  50% {
    box-shadow: 0 0 15px rgba(255, 107, 53, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(255, 107, 53, 0.5);
  }
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* Image loading fade-in effect */
img {
  transition: opacity 0.3s ease;
}

img.lazy-load {
  opacity: 0;
}

img.lazy-loaded {
  opacity: 1;
}

/* Improved notification styling */
.notification-container {
  pointer-events: none;
}

.notification-toast {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(8px);
  max-width: calc(100% - 32px);
}

/* Common UI elements */
.badge {
  font-size: 0.75rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 100px;
}

.chip {
  display: inline-flex;
  align-items: center;
  padding: 0.35rem 0.75rem;
  border-radius: 100px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #f3f4f6;
  color: #374151;
}

.divider {
  height: 1px;
  width: 100%;
  background-color: #e5e7eb;
  margin: 1rem 0;
}

/* Card hover effects */
.product-card .product-image-container {
  overflow: hidden;
}

.product-card img {
  transition: transform 0.5s ease;
}

.product-card:hover img {
  transform: scale(1.05);
}

.product-card .quick-add-btn {
  transition: all 0.2s ease;
}

.product-card:hover .quick-add-btn {
  transform: translateY(-4px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-hover {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

/* Modal animations */
.modal-enter {
  animation: modalEnter 0.3s forwards;
}

@keyframes modalEnter {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}